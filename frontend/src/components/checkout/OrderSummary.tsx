import { installments } from '@/config/payment';
import { useCheckout } from '@/contexts/checkout/useCheckout';
import { formatCurrency, formatCurrencyToShow } from '@/lib/utils';
import { useAuth } from '@/contexts/auth/useAuth';
import { t } from '@/lib/translations.helper';
import { getBasePrice, getCurrentPlanOnMonthly, getTotalPrice } from '@/lib/plan.utils';
import { calculateYearlyBillingDate, calculateYearlyInstallmentsAmount } from '@/lib/subscription.utils';
import { PaymentMethod } from './types';
import { addMonths, formatDate } from '@/lib/date.utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Button } from '../ui/button';
import { HelpCircle } from 'lucide-react';

export function OrderSummary() {
  const { subscription } = useAuth();
  const {
    selectedPlan,
    additionalsCalculated,
    baseSubtotal,
    total,
    subtotal,
    discount,
    isUpgrade,
    isPartialUpgrade,
    isDowngrade,
    upgradeCredit,
    currentPlan,
    isCurrentYearly,
    plans,
    form,
  } =
    useCheckout();

  const isCreditCard = form.getValues('paymentMethod') === PaymentMethod.CREDIT_CARD;
  // const billingDay = isCreditCard ? new Date().getDate() : Number(form.watch('billingDay'));
  const billingDay = Number(form.watch('billingDay'));

  const { isYearly } = selectedPlan;

  const customFeatures = selectedPlan.customFeatures.map((feature) => {
    return {
      ...feature,
      additionals: feature.quantity - feature.included,
      totalPrice:
        (isYearly ? feature.yearlyPrice * 12 : feature.monthlyPrice) *
        (feature.quantity - feature.included),
    };
  });

  const selectedPlanInMonthly = getCurrentPlanOnMonthly(plans, selectedPlan);
  const maxInstallment = Math.max(...installments);
  const planPrice = getBasePrice(selectedPlan);
  const monthlyPrice = getTotalPrice(selectedPlanInMonthly) * 12;

  const changingBillingCycle = !!currentPlan && isCurrentYearly !== isYearly;

  // Calcular as datas
  const nextBillingDate = calculateYearlyBillingDate(billingDay);

  const endDate = subscription?.endDate ? new Date(subscription.endDate).getTime() : addMonths(nextBillingDate, maxInstallment - 1);
  const [firstInstallment, restInstallments] = calculateYearlyInstallmentsAmount({
    totalAmount: baseSubtotal,
    installments: maxInstallment,
    billingDay,
    creditDiscount: upgradeCredit || 0,
    paymentMethod: form.getValues('paymentMethod'),
    endDate,
    daysInCycle: isYearly ? 365 : 30,
  });

  const hasProportional = firstInstallment !== restInstallments;
  const totalInstallments = hasProportional ? maxInstallment - 1 : maxInstallment;

  return (
    <div className="rounded-lg border p-4 space-y-4">
      <div className="space-y-4 text-sm">
        <div className="flex justify-between text-sm border-b pb-1">
          <span>ITEM</span>
          <span>
            VALOR
            {' '}
            <span className="text-xs">
              ({isYearly ? 'ano' : 'mês'})
            </span>
          </span>
        </div>
        {(isUpgrade || isDowngrade) && (
          <span className="text-xs text-muted-foreground font-medium">
            Migrando de {currentPlan.name} {t(`interval.${isCurrentYearly ? 'yearly' : 'monthly'}`)}
            {' para '}
          </span>
        )}
        {isPartialUpgrade && (
          <span className="text-xs text-muted-foreground font-medium">
            Aumentando recursos do plano {currentPlan.name}
          </span>
        )}
        <div className="flex justify-between">
          <div className="flex flex-col">
            <span className="font-medium">{selectedPlan.name} {t(`interval.${isYearly ? 'yearly' : 'monthly'}`)} </span>
            <span className="text-xs font-normal text-green-600">
              {selectedPlan.leadsCount.toLocaleString()} leads
            </span>
          </div>
          <div className="flex flex-col">
            {isYearly && !isUpgrade && !isPartialUpgrade && selectedPlan.discount > 0 && (
              <span className="line-through text-muted-foreground text-xs">
                {formatCurrency(monthlyPrice)}
              </span>
            )}
            <span>{formatCurrency(planPrice)}</span>
          </div>
        </div>

        {customFeatures
          .filter((f) => f.additionals > 0)
          .map((feature) => (
            <div key={feature.id} className="flex justify-between text-sm">
              <div className="flex flex-col">
                <span className="font-medium text-xs">
                  {feature.quantity} {feature.name}
                </span>
                <span className="text-xs font-normal text-green-600">
                  {feature.included} incluídos + {feature.additionals} extras
                </span>
              </div>
              <span>{formatCurrency(feature.totalPrice)}</span>
            </div>
          ))}

        {additionalsCalculated
          .filter((c) => c.selected)
          .map((item) => (
            <div key={item.id} className="flex justify-between text-sm">
              <span>{item.name}</span>
              <span>{formatCurrency(item.price)}</span>
            </div>
          ))}

        {isYearly && subtotal != total && (isUpgrade || isDowngrade || isPartialUpgrade) && (
          <div className="flex justify-between items-center border-t pt-4">
            <div className="flex-1 ">Subtotal</div>
            <div className="text-right">
              <div className="flex flex-col">
                <p className="relative text-md text-green-600 font-medium">
                  {formatCurrency(subtotal)}
                </p>
              </div>
            </div>
          </div>
        )}

        {discount > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Desconto</span>
            <span>-{formatCurrency(discount)}</span>
          </div>
        )}

        {upgradeCredit > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Desconto no upgrade</span>
            <span>{formatCurrency(upgradeCredit)}</span>
          </div>
        )}

        <div className="border-b" />

        <div className="flex justify-between">
          <div className="flex items-center gap-2">
            <span className="flex-1 text-lg bg-clip-text font-medium">Total</span>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                  <HelpCircle className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md max-h-[85vh] flex flex-col">
                <DialogHeader className="flex-shrink-0">
                  <DialogTitle>Como calculamos o valor da sua assinatura</DialogTitle>
                </DialogHeader>
                <div className="flex-1 overflow-y-auto pr-2 space-y-4 text-sm [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-track]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:border-transparent [&::-webkit-scrollbar-thumb]:bg-clip-padding dark:[&::-webkit-scrollbar-track]:bg-gray-800 dark:[&::-webkit-scrollbar-thumb]:bg-gray-600 hover:[&::-webkit-scrollbar-thumb]:bg-gray-400 dark:hover:[&::-webkit-scrollbar-thumb]:bg-gray-500">
                  {/* Explicação do plano base */}
                  <div className="space-y-2">
                    <h4 className="font-medium">Plano Base</h4>
                    <div className="bg-muted p-3 rounded-lg">
                      <div className="flex justify-between">
                        <span>{selectedPlan.name} {t(`interval.${isYearly ? 'yearly' : 'monthly'}`)}</span>
                        <span>{formatCurrency(planPrice)}</span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {selectedPlan.leadsCount.toLocaleString()} leads incluídos
                      </p>
                      {isYearly && !isUpgrade && !isPartialUpgrade && selectedPlan.discount > 0 && (
                        <p className="text-xs text-green-600 mt-1">
                          Desconto anual aplicado (era {formatCurrency(monthlyPrice)})
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Recursos personalizados */}
                  {customFeatures.filter((f) => f.additionals > 0).length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Recursos Extras</h4>
                      <div className="bg-muted p-3 rounded-lg space-y-2">
                        {customFeatures
                          .filter((f) => f.additionals > 0)
                          .map((feature) => (
                            <div key={feature.id} className="flex justify-between text-xs">
                              <span>{feature.additionals} {feature.name} extras</span>
                              <span>{formatCurrency(feature.totalPrice)}</span>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}

                  {/* Adicionais */}
                  {additionalsCalculated.filter((c) => c.selected).length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Serviços Adicionais</h4>
                      <div className="bg-muted p-3 rounded-lg space-y-2">
                        {additionalsCalculated
                          .filter((c) => c.selected)
                          .map((item) => (
                            <div key={item.id} className="flex justify-between text-xs">
                              <span>{item.name}</span>
                              <span>{formatCurrency(item.price)}</span>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}

                  {/* Explicação de upgrade/downgrade */}
                  {(isUpgrade || isDowngrade || isPartialUpgrade) && (
                    <div className="space-y-2">
                      <h4 className="font-medium">
                        {isUpgrade && 'Upgrade de Plano'}
                        {isDowngrade && 'Downgrade de Plano'}
                        {isPartialUpgrade && 'Personalização do Plano'}
                      </h4>
                      <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
                        <p className="text-xs">
                          {isUpgrade && `Você está migrando do plano ${currentPlan?.name} para ${selectedPlan.name}.`}
                          {isDowngrade && `Você está migrando do plano ${currentPlan?.name} para ${selectedPlan.name}.`}
                          {isPartialUpgrade && `Você está adicionando recursos ao seu plano ${currentPlan?.name}.`}
                        </p>
                        {changingBillingCycle && (
                          <p className="text-xs mt-2 text-amber-600">
                            Como você está mudando o ciclo de cobrança, o novo plano será ativado apenas no final da sua assinatura atual.
                          </p>
                        )}
                        {!changingBillingCycle && hasProportional && (
                          <p className="text-xs mt-2 text-green-600">
                            O valor foi calculado proporcionalmente aos dias restantes da sua assinatura atual.
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Crédito de upgrade */}
                  {upgradeCredit > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Desconto no Upgrade</h4>
                      <div className="bg-green-50 dark:bg-green-950 p-3 rounded-lg">
                        <div className="flex justify-between text-xs">
                          <span>Crédito da assinatura atual</span>
                          <span className="text-green-600">-{formatCurrency(upgradeCredit)}</span>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          Valor proporcional não utilizado do seu plano atual
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Desconto */}
                  {discount > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Desconto Aplicado</h4>
                      <div className="bg-green-50 dark:bg-green-950 p-3 rounded-lg">
                        <div className="flex justify-between text-xs">
                          <span>Desconto promocional</span>
                          <span className="text-green-600">-{formatCurrency(discount)}</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Parcelamento */}
                  {isYearly && totalInstallments > 1 && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Parcelamento</h4>
                      <div className="bg-muted p-3 rounded-lg">
                        <p className="text-xs">
                          {hasProportional && firstInstallment > 0 ? (
                            <>
                              <strong>1ª parcela:</strong> {formatCurrency(firstInstallment)} (valor proporcional até {formatDate(nextBillingDate)})
                              <br />
                              <strong>Demais parcelas:</strong> {totalInstallments}x de {formatCurrency(restInstallments)}
                            </>
                          ) : (
                            <>
                              <strong>Parcelamento:</strong> {totalInstallments}x de {formatCurrency(restInstallments)}
                              <br />
                              <strong>Primeira cobrança:</strong> {formatDate(nextBillingDate)}
                            </>
                          )}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Total final */}
                  <div className="border-t pt-3">
                    <div className="flex justify-between font-medium">
                      <span>Valor Total</span>
                      <span className="text-green-600">{formatCurrency(total)}</span>
                    </div>
                    {upgradeCredit > 0 && (
                      <p className="text-xs text-muted-foreground mt-1">
                        A partir de {formatDate(subscription?.endDate)} será cobrado {formatCurrencyToShow(planPrice, isYearly)}/mês
                      </p>
                    )}
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
          <div className="text-right">

            <div className="flex flex-col">
              {/* Parcelamento PIX ou Boleto */}
              {isYearly && totalInstallments > 1 && !isCreditCard && (
                <>
                  {/* Total do plano anual */}
                  <p className="text-md font-medium text-green-600">
                    <span className="text-muted-foreground"> a vista </span>
                    {formatCurrency(total)}
                  </p>
                  <span className="text-muted-foreground">ou</span>

                  {/* Entrada proporcional (se existir) */}
                  {hasProportional && firstInstallment > 0 && (
                    <div className="text-xs font-normal text-muted-foreground">
                      <span className="text-muted-foreground">1x de </span>
                      <span className="text-green-600 text-sm font-bold">
                        {formatCurrency(firstInstallment)}
                      </span>
                      <div className="text-xs text-amber-600 font-medium mt-1">
                        * valor proporcional até {formatDate(nextBillingDate)}
                      </div>
                    </div>
                  )}

                  {/* Parcelas (após a entrada) */}
                  {firstInstallment > 0 ? (
                    <div className="text-xs font-normal mt-1">
                      <div className="text-green-600">
                        mais {totalInstallments}x de&nbsp;
                        <span className="text-sm font-bold">
                          {formatCurrency(restInstallments)}
                        </span>
                      </div>
                      <span className="text-xs text-amber-600">
                        * iniciando em {formatDate(nextBillingDate)}
                      </span>
                    </div>
                  ) : (
                    <div className="text-xs font-normal mt-1">
                      <div className="text-green-600">
                        {totalInstallments}x de&nbsp;
                        <span className="text-sm font-bold">
                          {formatCurrency(restInstallments)}
                        </span>
                      </div>
                      <span className="text-xs">
                        com primeira cobrança em {formatDate(nextBillingDate)}
                      </span>
                    </div>
                  )}
                </>
              )}

              {/* Parcelamento Cartão de Crédito */}
              {totalInstallments > 1 && isCreditCard && (
                <>
                  {/* Parcelas */}
                  <div className="text-xs text-green-600 font-normal mt-1">
                    {totalInstallments}x de&nbsp;
                    <span className="text-sm font-bold">
                      {formatCurrency(restInstallments)}
                    </span>
                  </div>
                  <span className="text-muted-foreground">ou</span>

                  {/* Total do plano anual */}
                  <p className="text-md font-medium text-white">
                    <span className="text-muted-foreground"> a vista </span>
                    {formatCurrency(total)}
                  </p>
                </>
              )}

              {/* Pagamento à vista */}
              {maxInstallment === 1 && (
                <p className="text-md text-green-600 font-medium mt-2">
                  Pagamento à vista: {formatCurrency(total)}
                </p>
              )}
            </div>

            {
              !isYearly && (
                <div className="flex flex-col">
                  <p className="relative text-md text-green-600 font-medium">
                    {formatCurrency(total)}
                  </p>
                </div>
              )
            }
          </div>
        </div>

        {changingBillingCycle && (
          <div className="flex justify-center align-center">
            <p className="text-xs text-amber-600 font-medium mr-2">
              * será cobrado e aplicado em {formatDate(subscription.endDate)}
            </p>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle size={16} />
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    O novo plano será ativado após o término da sua assinatura atual.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}

        {upgradeCredit > 0 && (
          <div className="flex justify-center text-green-600">
            <p className="text-xs text-amber-600 font-medium mt-1">
              * a partir de {formatDate(subscription.endDate)}
              {' - '}
              {formatCurrencyToShow(planPrice, isYearly)}
              /mês
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
